import { open, type Database } from 'sqlite';
import { getIndexSqlitePath, getOriginalIndexSqlitePath } from '../../util/paths';
import * as fs from 'node:fs';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';

export type DatabaseConnection = Database<import('sqlite3').Database>;

interface ColumnDefinition {
  name: string;
  type: string;
  nullable: boolean;
  primaryKey?: boolean;
  autoIncrement?: boolean;
  defaultValue?: string;
  check?: string;
}
const logger = new Logger('SqliteDb');

export class SqliteDb {
  static db: DatabaseConnection | null = null;
  private static indexSqlitePath: string;
  private static initPromise: Promise<DatabaseConnection> | null = null;

  // Define a central table schema structure
  private static tableSchemas: {
    [tableName: string]: {
      columns: ColumnDefinition[];
    };
  } = {
    repo_status: {
      columns: [
        // 主键ID，自增
        { name: 'id', type: 'INTEGER', nullable: false, primaryKey: true, autoIncrement: true },
        // Git仓库URL
        { name: 'git_url', type: 'TEXT', nullable: false },
        // 本地目录路径
        { name: 'dir_path', type: 'TEXT', nullable: false },
        // Git分支名称
        { name: 'branch', type: 'TEXT', nullable: false },
        // Git提交ID
        { name: 'commit_id', type: 'TEXT', nullable: false },
        // 更新时间戳
        { name: 'updated_at', type: 'INTEGER', nullable: false },
        // 创建时间戳
        { name: 'created_at', type: 'INTEGER', nullable: false },
        // IDE版本
        { name: 'ide_version', type: 'TEXT', nullable: false },
        // 插件版本
        { name: 'plugin_version', type: 'TEXT', nullable: false },
        // 平台类型：xcode, vscode, jetbrains
        { name: 'platform', type: 'TEXT', nullable: false, check: "platform IN ('xcode', 'vscode', 'jetbrains')" },
        // 总文件数
        { name: 'total_files', type: 'INTEGER', nullable: true, defaultValue: '0' },
        // 已完成文件数
        { name: 'done_files', type: 'INTEGER', nullable: true, defaultValue: '0' },
        // 是否正在构建中
        { name: 'is_building', type: 'BOOLEAN', nullable: true, defaultValue: 'FALSE' },
        // 索引状态：0-可以正常索引，1-超过5k条数据
        { name: 'status', type: 'TEXT', nullable: true, defaultValue: "'0'" }
      ]
    },
    file_status: {
      columns: [
        // 主键ID，自增
        { name: 'id', type: 'INTEGER', nullable: false, primaryKey: true, autoIncrement: true },
        // Git仓库URL
        { name: 'git_url', type: 'TEXT', nullable: false },
        // 本地目录路径
        { name: 'dir_path', type: 'TEXT', nullable: false },
        // 文件名
        { name: 'filename', type: 'TEXT', nullable: false },
        // 文件路径
        { name: 'filepath', type: 'TEXT', nullable: false },
        // 文件哈希值
        { name: 'hash', type: 'TEXT', nullable: false },
        // 文件状态：indexing-索引中，indexed-已索引，deleted-已删除
        { name: 'status', type: 'TEXT', nullable: false, check: "status IN ('indexing', 'indexed', 'deleted')" },
        // 更新时间戳
        { name: 'updated_at', type: 'INTEGER', nullable: false },
        // 创建时间戳
        { name: 'created_at', type: 'INTEGER', nullable: false }
      ]
    },
    tasks: {
      columns: [
        // 主键ID，自增
        { name: 'id', type: 'INTEGER', nullable: false, primaryKey: true, autoIncrement: true },
        // 任务ID
        { name: 'task_id', type: 'TEXT', nullable: false },
        // 文件路径
        { name: 'filepath', type: 'TEXT', nullable: false },
        // Git仓库URL
        { name: 'git_url', type: 'TEXT', nullable: false },
        // 创建时间戳
        { name: 'created_at', type: 'INTEGER', nullable: false },
        // 更新时间戳
        { name: 'updated_at', type: 'INTEGER', nullable: false },
        // 任务状态：pending-等待中，indexing-索引中
        { name: 'status', type: 'TEXT', nullable: false, check: "status IN ('pending', 'indexing')" },
        // 文件操作类型：modify-修改，delete-删除，create-创建
        { name: 'file_action', type: 'TEXT', nullable: false, check: "file_action IN ('modify', 'delete', 'create')" },
        // 本地目录路径
        { name: 'dir_path', type: 'TEXT', nullable: false },
        // 重试次数
        { name: 'retry_count', type: 'INTEGER', nullable: false, defaultValue: '0' }
      ]
    },
    user_status: {
      columns: [
        // 主键ID，自增
        { name: 'id', type: 'INTEGER', nullable: false, primaryKey: true, autoIncrement: true },
        // 用户状态：0,1,2 表示不同状态
        { name: 'status', type: 'INTEGER', nullable: false, check: 'status IN (0, 1, 2)' },
        // 本地目录路径
        { name: 'dir_path', type: 'TEXT', nullable: false },
        // 总文件数
        { name: 'total_file_num', type: 'INTEGER', nullable: false },
        // 成功处理的文件数
        { name: 'success_file_num', type: 'INTEGER', nullable: false },
        // 失败处理的文件数
        { name: 'failed_file_num', type: 'INTEGER', nullable: false },
        // 额外信息，JSON格式
        { name: 'extra', type: 'TEXT', nullable: true }
      ]
    },
    cache_status: {
      columns: [
        // 主键ID，自增
        { name: 'id', type: 'INTEGER', nullable: false, primaryKey: true, autoIncrement: true },
        // 文件路径
        { name: 'filepath', type: 'TEXT', nullable: false },
        // 删除时间戳
        { name: 'delete_time', type: 'INTEGER', nullable: false },
        // 文件MD5哈希值
        { name: 'file_md5', type: 'TEXT', nullable: false },
        // 本地目录路径
        { name: 'dir_path', type: 'TEXT', nullable: false }
      ]
    }
  };

  static async initTables() {
    const dirPath = GlobalConfig.getConfig().getRepoPath();
    const db = await SqliteDb.get(dirPath);
    // 直接使用 tableSchemas 的键作为表名列表
    const tableNames = Object.keys(SqliteDb.tableSchemas);

    for (const tableName of tableNames) {
      await SqliteDb.initTable(db, tableName);
    }
  }

  private static async initTable(db: DatabaseConnection, tableName: string) {
    // 检查表是否存在
    const tableExists = await db.get(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`, [tableName]);

    if (!tableExists) {
      // 如果表不存在，创建单个表而不是所有表
      await SqliteDb.createTable(db, tableName);
      return;
    }

    // 获取当前表的列信息
    const tableInfo = await db.all(`PRAGMA table_info(${tableName})`);
    const currentColumnNames = new Set(tableInfo.map((col) => col.name));

    // 获取表结构定义
    const schema = SqliteDb.tableSchemas[tableName];
    if (!schema) return;

    // 找出需要添加的列
    const columnsToAdd = schema.columns.filter((column) => !currentColumnNames.has(column.name));

    if (columnsToAdd.length === 0) return; // 没有新列需要添加

    // 添加新列
    try {
      await db.exec('BEGIN TRANSACTION');

      for (const column of columnsToAdd) {
        logger.info(`initTable: ${tableName} add column: ${column.name}`);
        await SqliteDb.addColumn(db, tableName, column);
      }

      await db.exec('COMMIT');
    } catch (error: unknown) {
      await db.exec('ROLLBACK');
      throw new Error(
        `Failed to add columns to ${tableName}: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  // 创建单个表的方法
  private static async createTable(db: DatabaseConnection, tableName: string) {
    const schema = SqliteDb.tableSchemas[tableName];
    if (!schema) return;

    const columnDefinitions = schema.columns
      .map((column) => {
        let def = `${column.name} ${column.type}`;

        if (column.primaryKey) {
          def += ' PRIMARY KEY';
          if (column.autoIncrement) {
            def += ' AUTOINCREMENT';
          }
        }

        if (!column.nullable) {
          def += ' NOT NULL';
        }

        if (column.defaultValue !== undefined) {
          def += ` DEFAULT ${column.defaultValue}`;
        }

        if (column.check) {
          def += ` CHECK(${column.check})`;
        }

        return def;
      })
      .join(',\n        ');

    await db.exec(`
      CREATE TABLE IF NOT EXISTS ${tableName} (
        ${columnDefinitions}
      )
    `);
  }

  private static async addColumn(
    db: DatabaseConnection,
    tableName: string,
    column: {
      name: string;
      type: string;
      nullable: boolean;
      primaryKey?: boolean;
      autoIncrement?: boolean;
      defaultValue?: string;
      check?: string;
    }
  ) {
    let sql = `ALTER TABLE ${tableName} ADD COLUMN ${column.name} ${column.type}`;

    if (!column.nullable) {
      sql += ' NOT NULL';
    }

    if (column.defaultValue !== undefined) {
      sql += ` DEFAULT ${column.defaultValue}`;
    } else if (!column.nullable) {
      // 如果没有指定默认值但是非空，则使用类型默认值
      const defaultValue = SqliteDb.getDefaultValueForType(column.type, column.nullable);
      sql += ` DEFAULT ${defaultValue}`;
    }

    if (column.check) {
      sql += ` CHECK(${column.check})`;
    }

    await db.exec(sql);
  }

  private static getDefaultValueForType(type: string, nullable: boolean): string {
    if (nullable) {
      return 'NULL';
    }

    switch (type) {
      case 'INTEGER':
        return '0';
      case 'TEXT':
        return "''";
      case 'BOOLEAN':
        return 'FALSE';
      default:
        return 'NULL';
    }
  }

  static async get(dirPath?: string) {
    if (!dirPath) {
      dirPath = GlobalConfig.getConfig().getRepoPath();
    }
    if (SqliteDb.initPromise) {
      return SqliteDb.initPromise;
    }
    if (SqliteDb.db && fs.existsSync(SqliteDb.indexSqlitePath)) {
      return SqliteDb.db;
    }
    let sqlitePath = getIndexSqlitePath();
    if (!GlobalConfig.getConfig().getIsDBImgrated()) {
      sqlitePath = getOriginalIndexSqlitePath();
    }
    // 开始新的初始化
    SqliteDb.initPromise = (async () => {
      SqliteDb.indexSqlitePath = sqlitePath;
      const sqlite3 = await import('sqlite3');
      SqliteDb.db = await open({
        filename: SqliteDb.indexSqlitePath,
        driver: sqlite3.Database
      });
      return SqliteDb.db;
    })();

    try {
      return await SqliteDb.initPromise;
    } finally {
      // 初始化完成后清除Promise
      SqliteDb.initPromise = null;
    }
  }
  // 重试 sqlite 连接
  static async resetDBConnection() {
    if (SqliteDb.db) {
      await SqliteDb.db.close();
      SqliteDb.db = null;
    }
    SqliteDb.indexSqlitePath = '';
    await SqliteDb.get();
  }
}
