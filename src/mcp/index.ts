import { z } from 'zod';
import { CallToolResultSchema } from '@modelcontextprotocol/sdk/types.js';
import {
  DEFAULT_MCP_TIMEOUT_SECONDS,
  McpServerSimpleConfig,
  McpSettingsSimpleSchema,
  ServerConfigSchema
} from './settingSchema';
import { IMcpClient, InstallMcpParams, MarketMcpDetail, McpServer, McpServerChangeEventDetail, McpToolCallResponse } from './types';
import { ResponseBase, STATUS } from '@/protocol/index.d';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { IMessenger } from '@/protocol/messenger';
import {
  checkMcpSettingsFile,
  writeSettingsFile,
  readSettingsFile,
  writeConfigFile,
  readConfigFile,
  watchFile
} from './fileHelper';
import { mergeMcpConfigAndSettings, readAndValidateMcpSettingsFile, readMcpConfigFile } from './parserHelper';
import { secondsToMs } from './utils';
import { getMcpSettingsPath } from '@/util/paths';
import { McpConnectionManager } from './connection';
import { MCPLogger } from '@/util/log';
import { safeJsonStringify } from './parserHelper/jsonParserHelper';
import { throttle } from 'lodash-es';
import { fetchAvailableMcpListByMarket, fetchMcpDetailByMarket } from './market/index';

/** 全局MCP客户端实例 */
let mcpClientInstance: IMcpClient;

/**
 * MCP客户端实现类
 * 提供了MCP服务器的管理和工具调用功能
 */
export class McpClient implements IMcpClient {
  /** 是否正在进行连接操作 */
  private isConnecting: boolean = false;

  /** 消息通信实例 */
  private messenger?: IMessenger<ToCoreProtocol, FromCoreProtocol>;

  /** 配置文件监听器 */
  private mcpSettingsWatcher?: () => void;

  /** MCP客户端版本号 */
  public clientVersion = '1.0.0';

  /** 配置文件解析是否出错 */
  public parseError = false;

  /** 配置文件解析错误信息 */
  public parseErrorMessage = '';

  /** 上一次服务器变更事件详情 */
  private prevNotifyServerChangeDetailJSON?: string;

  /** 上一次写入的配置文件内容 */
  private prevConfigFile = '';

  /** MCP连接管理器实例 */
  private connectionManager: McpConnectionManager;

  private logger = new MCPLogger(`mcp-${this.clientVersion}`);

  /**
   * 获取McpClient的全局单例实例
   * 确保整个应用中只有一个MCP客户端实例
   *
   * @returns 全局唯一的MCP客户端实例
   */
  static getInstance(): IMcpClient {
    if (!mcpClientInstance) {
      mcpClientInstance = new McpClient();
    }
    return mcpClientInstance;
  }

  /**
   * 私有构造函数
   * 初始化MCP客户端，包括：
   * - 创建连接管理器
   * - 设置配置文件监听
   * - 初始化服务器连接
   */
  private constructor() {
    this.connectionManager = new McpConnectionManager(this.clientVersion, () => this.notifyServerChange());
    this.watchMcpSettingsFile();
    this.initializeMcpServers();
  }

  /**
   * 释放客户端资源
   * 清理所有连接、监听器和其他资源
   */
  public async dispose(): Promise<void> {
    this.removeAllFileWatchers();
  }

  /**
   * 设置消息通信实例
   * 用于与核心进程进行通信
   *
   * @param messenger - 消息通信实例
   */
  public setMessenger(messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {
    this.messenger = messenger;
  }

  private throttledSendSingleChangeMessage = (() => {
    return throttle(
      async (message) => {
        await this.messenger?.send('mcp/mcpServerChange', message);
      },
      50,
      {
        leading: true,
        trailing: true
      }
    );
  })();

  /**
   * 通知服务器状态变更
   * 向核心进程发送服务器状态变更事件
   * 包括服务器列表、错误状态等信息
   */
  private async notifyServerChange(): Promise<void> {
    if (!this.messenger) {
      this.logger.error('mcp/mcpServerChange 通知失败，messenger 未设置');
      return;
    }

    let mcpServers = await this.getOrderMcpServers();

    const currentChangeEventDetail: McpServerChangeEventDetail = {
      code: 0,
      isError: this.parseError,
      message: this.parseErrorMessage || '',
      mcpServers: this.parseError ? [] : mcpServers
    };

    const currentChangeEventDetailJSON = safeJsonStringify(currentChangeEventDetail);

    if (currentChangeEventDetailJSON === this.prevNotifyServerChangeDetailJSON) {
      this.logger.debug('notifyServerChange 相同');
      return;
    }

    this.throttledSendSingleChangeMessage(currentChangeEventDetail);

    this.logger.info('mcp/mcpServerChange');

    this.prevNotifyServerChangeDetailJSON = currentChangeEventDetailJSON;
  }

  /**
   * 写入MCP配置文件
   * 只在配置内容发生变化时才进行写入
   *
   * @param config - 要写入的配置内容
   */
  private async toWriteMcpConfigFile(config: z.infer<typeof McpSettingsSimpleSchema>) {
    try {
      let configContent = JSON.stringify(config, null, 2);

      if (configContent !== this.prevConfigFile) {
        await writeConfigFile(configContent);
        this.prevConfigFile = configContent;
      }
    } catch (err) {
      this.logger.error(`写入MCP配置文件失败: ${err}`);
    }
  }

  /**
   * 获取MCP设置文件的完整路径
   *
   * @returns 包含设置文件路径的响应对象
   * @throws 当无法获取或访问设置文件路径时
   */
  public async getSettingsPath(): Promise<ResponseBase<string>> {
    try {
      const settingsPath = await checkMcpSettingsFile();
      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: settingsPath
      };
    } catch (error) {
      this.logger.error(error);
      return {
        status: STATUS.FAILED,
        message: '获取 mcp 设置文件地址失败',
        data: ''
      };
    }
  }

  /**
   * 切换指定MCP服务器的启用/禁用状态
   *
   * @param params.serverName - 目标服务器名称
   * @param params.disabled - 是否禁用
   * @returns 操作结果
   * @throws 当服务器不存在或状态切换失败时
   */
  public async toggleMcpServer(params: { serverName: string; disabled: boolean }): Promise<ResponseBase<boolean>> {
    try {
      await this.toggleConnectionDisabled(params.serverName, params.disabled);

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error(`切换MCP Server ${params.serverName} 状态失败`, error);
      return {
        status: STATUS.FAILED,
        message: '切换MCP Server状态失败',
        data: false
      };
    }
  }

  /**
   * 重启指定的MCP服务器
   *
   * @param params.serverName - 要重启的服务器名称
   * @returns 重启操作结果
   * @throws 当服务器不存在或重启操作失败时
   */
  public async restartMcpServer(params: { serverName: string }): Promise<ResponseBase<boolean>> {
    try {
      await this.restartConnection(params.serverName);

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error(`重启MCP Server ${params.serverName} 失败`, error);
      return {
        status: STATUS.FAILED,
        message: '重启MCP Server失败',
        data: false
      };
    }
  }

  /**
   * 删除指定的MCP服务器
   *
   * @param params.serverName - 要删除的服务器名称
   * @returns 删除操作结果
   * @throws 当服务器不存在或删除操作失败时
   */
  public async deleteMcpServer(params: { serverName: string }): Promise<ResponseBase<boolean>> {
    this.logger.debug(`删除MCP Server ${params.serverName} 中。。。`);

    try {
      await this.deleteServer(params.serverName);

      this.logger.debug(`删除MCP Server ${params.serverName} 成功`);

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error(`删除MCP Server ${params.serverName} 失败`, error);
      return {
        status: STATUS.FAILED,
        code: -1,
        message: '删除MCP Server失败',
        data: false
      };
    }
  }

  async installMcp(params: InstallMcpParams): Promise<ResponseBase<boolean>> {
    try {
      this.logger.info('call installMcp', params);
      // 1. 批量校验所有 mcpServers
      for (const [name, config] of Object.entries(params.mcpServers)) {
        const result = ServerConfigSchema.safeParse(config);
        if (!result.success) {
          this.logger.error(`安装MCP ${name} 失败`, config, result.error);
          return {
            status: STATUS.FAILED,
            code: -1,
            message: `MCP 字段不合法: ${name}`,
            data: false
          };
        }
      }

      // 2. 读取原有 setting
      const content = await readSettingsFile();
      const setting = JSON.parse(content || '{}');
      const oldServers = setting.mcpServers || {};

      // 3. 合并新老 mcpServers（新优先）
      const mergedServers: Record<string, any> = {};
      // 先插入新服务器，保持其顺序
      for (const key of Object.keys(params.mcpServers)) {
        mergedServers[key] = params.mcpServers[key];
      }
      // 再插入旧服务器中未被覆盖的
      for (const key of Object.keys(oldServers)) {
        if (!(key in params.mcpServers)) {
          mergedServers[key] = oldServers[key];
        }
      }
      setting.mcpServers = mergedServers;

      // 4. 写入文件
      await writeSettingsFile(JSON.stringify(setting, null, 2));

      // 5. 校验一次
      await readAndValidateMcpSettingsFile();

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error('installMcp 失败', error);
      return {
        status: STATUS.FAILED,
        code: -1,
        message: 'installMcp 失败',
        data: false
      };
    }
  }

  async fetchAvailableMcpListByMarket(params: { page?: number; pageSize?: number; searchKeyword?: string; }): Promise<ResponseBase<{ page?: number; pageSize?: number; total?: number; records?: MarketMcpDetail[]; }>> {
    this.logger.info('call fetchAvailableMcpListByMarket', params);
    // 1. 批量校验所有 mcpServers
    return await fetchAvailableMcpListByMarket(params);
  }

  async fetchMcpDetailByMarket(params: { serverId: string; }): Promise<ResponseBase<MarketMcpDetail>> {
    this.logger.info('call fetchMcpDetailByMarket', params);

    if (!params || !params.serverId) {
      return {
        status: STATUS.FAILED,
        code: -1,
        message: 'serverId 不能为空',
        data: undefined
      };
    }

    return await fetchMcpDetailByMarket(params)
  }

  /**
   * 获取所有已打开且已连接的服务器列表
   *
   * @returns 已连接的服务器列表
   */
  public getOpenedAndConnectedServers(): McpServer[] {
    return this.connectionManager
      .getConnections()
      .filter((conn) => !conn.server.disabled)
      .filter((conn) => conn.server.status === 'connected')
      .map((conn) => conn.server);
  }

  /**
   * 调用指定服务器上的工具
   *
   * @param serverName - 目标服务器名称
   * @param toolName - 要调用的工具名称
   * @param toolArguments - 工具调用参数
   * @returns 工具执行结果
   * @throws 当工具调用失败时
   */
  async callTool(
    serverName: string,
    toolName: string,
    toolArguments?: Record<string, unknown>
  ): Promise<McpToolCallResponse> {
    const connection = this.connectionManager.getConnection(serverName);
    if (!connection || !connection.client) {
      throw new Error(`${serverName} 未连接`);
    }

    if (connection.server.disabled) {
      throw new Error(`${serverName} 已禁用`);
    }

    const timeout = secondsToMs(DEFAULT_MCP_TIMEOUT_SECONDS);

    const response = await connection.client.request(
      {
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: toolArguments
        }
      },
      CallToolResultSchema,
      {
        timeout
      }
    );

    return response;
  }

  /**
   * 获取服务器列表
   * 可选择按状态筛选
   *
   * @param filterStatus - 可选的服务器状态过滤条件
   * @returns 包含服务器列表和错误状态的响应对象
   */
  public async getDisplayServers(
    filterStatus?: McpServer['status']
  ): Promise<ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>> {
    try {
      if (this.parseError) {
        return {
          status: STATUS.FAILED,
          code: -1,
          message: this.parseErrorMessage || '',
          data: {
            isError: this.parseError,
            mcpServers: []
          }
        };
      }

      const displayServers = await this.getOrderMcpServers();

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: {
          mcpServers: displayServers,
          isError: false
        }
      };
    } catch (err) {
      return {
        status: STATUS.FAILED,
        code: -1,
        message: '获取MCP服务列表失败',
        data: {
          mcpServers: [],
          isError: true
        }
      };
    }
  }

  /**
   * 获取按配置文件中的顺序排序的服务器列表
   *
   * @returns 排序后的服务器列表
   */
  private async getOrderMcpServers(): Promise<McpServer[]> {
    try {
      const config = await this.readAndValidateMcpConfigFile();
      const serverOrder = Object.keys(config?.mcpServers || {});
      return this.connectionManager
        .getConnections()
        .sort((a, b) => {
          const indexA = serverOrder.indexOf(a.server.name);
          const indexB = serverOrder.indexOf(b.server.name);
          return indexA - indexB;
        })
        .map((connection) => connection.server);
    } catch (error) {
      return [];
    }
  }

  /**
   * 读取并验证MCP配置文件
   * 合并设置文件和配置文件的内容
   *
   * @returns 合并后的配置对象
   */
  private async readAndValidateMcpConfigFile(): Promise<z.infer<typeof McpSettingsSimpleSchema> | undefined> {
    try {
      const { isError, message, content } = await readAndValidateMcpSettingsFile();

      this.parseError = isError;
      this.parseErrorMessage = message || '';

      const config = await readMcpConfigFile();

      let mergedConfig = content;
      if (content) {
        mergedConfig = mergeMcpConfigAndSettings(content, config);
      }

      this.logger.debug('mergedConfig', mergedConfig);
      return mergedConfig;
    } catch (error) {
      this.logger.error('readAndValidateMcpConfigFile', error);
      this.parseError = true;
      this.parseErrorMessage = '读取或验证MCP设置文件时发生错误';
      return undefined;
    }
  }

  /**
   * 移除所有文件监听器
   */
  private async removeAllFileWatchers(): Promise<void> {
    this.mcpSettingsWatcher?.();
  }

  /**
   * 监听MCP设置文件变化
   * 当配置文件发生变化时自动重新加载服务器连接
   */
  private async watchMcpSettingsFile(): Promise<void> {
    this.mcpSettingsWatcher = watchFile(getMcpSettingsPath(), async () => {
      this.logger.info('setting 文件变更');
      const settings = await this.readAndValidateMcpConfigFile();

      if (settings) {
        await this.updateServerConnections(settings.mcpServers);
      } else {
        this.logger.error('MCP 配置文件读取失败');
      }

      this.notifyServerChange();
    });
  }

  /**
   * 删除指定的MCP服务器配置
   * 从配置文件中移除服务器并清理相关资源
   *
   * 删除流程：
   * 1. 读取当前配置文件
   * 2. 从配置中移除指定服务器
   * 3. 删除服务器连接
   * 4. 保存更新后的配置
   * 5. 通知服务器变更
   *
   * @param serverName - 要删除的服务器名称
   * @throws 当删除服务器配置失败时
   */
  private async deleteServer(serverName: string): Promise<void> {
    try {
      const content = await readSettingsFile();

      const setting = JSON.parse(content || '{}');

      if (setting.mcpServers[serverName]) {
        delete setting.mcpServers[serverName];

        await writeSettingsFile(JSON.stringify(setting, null, 2));

        await this.deleteServerConfig(serverName);

        this.notifyServerChange();

        const config = await this.readAndValidateMcpConfigFile();

        await this.updateServerConnections(config?.mcpServers || {});
      }
    } catch (error) {
      this.logger.error(`删除MCP Server ${serverName} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除服务器配置
   * 从配置文件中移除服务器配置信息
   *
   * @param serverName - 要删除配置的服务器名称
   */
  private async deleteServerConfig(serverName: string) {
    try {
      const configContent = await readConfigFile();
      const config = JSON.parse(configContent || '{}');

      if (config.mcpServers[serverName]) {
        delete config.mcpServers[serverName];
        await this.toWriteMcpConfigFile(config);
      }
    } catch (error) {
      this.logger.error(`删除MCP Server ${serverName} 配置失败`, error);
    }
  }

  /**
   * 初始化所有MCP服务器连接
   * 读取配置并建立与各个服务器的连接
   */
  private async initializeMcpServers(): Promise<void> {
    try {
      if (await checkMcpSettingsFile()) {
        const settings = await this.readAndValidateMcpConfigFile();

        if (!settings) {
          return;
        }
        await this.updateServerConnections(settings.mcpServers);
      }
    } catch (error) {
      this.logger.error('初始化MCP Server失败', error);
    }
  }

  /**
   * 更新服务器连接
   * 根据新的配置更新现有连接：
   * 1. 删除已移除的服务器连接
   * 2. 创建所有新的连接实例
   * 3. 依次建立实际连接
   *
   * @param newServers - 新的服务器配置
   */
  private async updateServerConnections(newServers: Record<string, McpServerSimpleConfig>): Promise<void> {
    this.isConnecting = true;

    try {
      const currentNames = this.connectionManager.getConnections().map((conn) => conn.server.name);
      const newNames = new Set(Object.keys(newServers));

      // 删除已移除的服务器
      for (const name of currentNames) {
        if (!newNames.has(name)) {
          await this.connectionManager.removeConnection(name);
          await this.deleteServerConfig(name);
          this.logger.debug(`删除mcp server ${name} 的连接`);
        }
      }

      const needRestart = new Set<string>();

      // 先创建所有新的连接实例
      for (const [name, config] of Object.entries(newServers)) {
        const currentConnection = this.connectionManager.getConnection(name);

        // 是否是新增
        if (!currentConnection) {
          // 新增服务器，创建连接实例
          await this.connectionManager.createConnection(name, config);
          needRestart.add(name);
          continue;
        }

        // 是否是更新
        let isConfigChanged = false;
        try {
          const newData = ServerConfigSchema.safeParse(config).data;
          if (currentConnection.server.config !== JSON.stringify(newData)) {
            isConfigChanged = true;
          }
        } catch (error) {
          isConfigChanged = true;
        }

        if (isConfigChanged) {
          // 更新服务器配置，重新创建连接实例
          await this.connectionManager.removeConnection(name);
          await this.connectionManager.createConnection(name, config);
          this.logger.debug(`更新mcp server ${name} 的连接实例`);
          needRestart.add(name);
          continue;
        }

        // 其余不用操作
      }

      // 依次建立实际连接
      for (const connection of this.connectionManager.getConnections()) {
        if (!needRestart.has(connection.server.name)) {
          continue;
        }

        if (!connection.server.disabled) {
          try {
            await this.connectionManager.establishConnection(connection);
            this.logger.debug(`建立MCP Server ${connection.server.name} 实际连接`);
          } catch (err) {
            this.logger.error(`连接MCP Server ${connection.server.name} 失败`, err);
          } finally {
            needRestart.delete(connection.server.name);
          }
        }
      }
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * 重新建立指定服务器的连接
   * 断开现有连接并重新建立新的连接
   *
   * @param serverName - 服务器名称
   * @throws 当重新连接失败时
   */
  private async restartConnection(serverName: string): Promise<void> {
    this.isConnecting = true;

    const connection = this.connectionManager.getConnection(serverName);
    const config = connection?.server.config;

    if (config) {
      if (connection?.server.disabled) {
        return await this.toggleConnectionDisabled(serverName, false);
      }

      connection.server.status = 'connecting';
      connection.server.error = '';
      this.notifyServerChange();

      try {
        this.logger.debug(`重启MCP Server ${serverName}`);

        await this.connectionManager.removeConnection(serverName);
        const newConnection = await this.connectionManager.createConnection(serverName, JSON.parse(config));
        await this.connectionManager.establishConnection(newConnection, true);
      } catch (error) {
        this.logger.error(`重启连接失败: ${serverName}:`, error);
      }
    }
    this.isConnecting = false;
    this.notifyServerChange();
  }

  /**
   * 切换指定服务器的启用/禁用状态
   * 更新配置文件并通过连接管理器处理连接状态
   *
   * @param serverName - 服务器名称
   * @param disabled - 是否禁用
   * @throws 当切换状态失败时
   */
  private async toggleConnectionDisabled(serverName: string, disabled: boolean): Promise<void> {
    try {
      const config = await this.readAndValidateMcpConfigFile();
      if (!config) {
        throw new Error('Failed to read or validate MCP settings');
      }

      if (config.mcpServers[serverName]) {
        // 更新配置文件
        config.mcpServers[serverName].disabled = disabled;
        await this.toWriteMcpConfigFile(config);

        // 切换连接状态
        await this.connectionManager.toggleConnectionState(serverName, disabled);

        this.logger.debug(`切换MCP Server ${serverName} disabled 状态为 ${disabled}`);
        return;
      }

      this.logger.error(`MCP 配置文件中不存在 ${serverName}`);
      throw new Error(`MCP 配置文件中不存在 ${serverName}`);
    } catch (error) {
      this.logger.error(`切换连接状态失败: ${serverName}:`, error);
      throw error;
    }
  }
}

export default McpClient;
