/**
 * 接口文档 https://docs.corp.kuaishou.com/k/home/<USER>/fcADQt6v63UQqWBZNL1YVlQ-B
 */

import axios from 'axios';
import { MarketMcpDetail } from '../types';

// TODO: 上线之前要替换成线上
export const MarketHost = 'https://wanqing-test-api.staging.kuaishou.com';

export type MarketResponse<T> = {
  code: number;
  message: string;
  data: T;
};

export const getCommonHeaders = () => {
  return {
    'Content-Type': 'application/json'
  };
};

export async function fetchAvailableMcpListByMarketApi(params: {
  page?: number;
  pageSize?: number;
  searchKeyword?: string;
}): Promise<
  MarketResponse<{
    page?: number;
    pageSize?: number;
    total?: number;
    records?: MarketMcpDetail[];
  }>
> {
  const { page = 1, pageSize = 100, searchKeyword = '' } = params || {};
  const url = `${MarketHost}/openapi/v1/mcp-server-manager/mcp-kwaipilot/list-server`;
  const res = await axios.post(url, { page, pageSize, searchKeyword }, { headers: { ...getCommonHeaders() } });
  return res.data;
}

export async function fetchMcpDetailByMarketApi(params: {
  serverId: string;
}): Promise<MarketResponse<MarketMcpDetail>> {
  const { serverId } = params;
  const url = `${MarketHost}/openapi/v1/mcp-server-manager/mcp-kwaipilot/server/info`;
  const res = await axios.get(url, {
    params: { serverId },
    headers: {
      ...getCommonHeaders()
    }
  });
  return res.data;
}
