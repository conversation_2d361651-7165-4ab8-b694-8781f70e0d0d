import { getErrorLogsPath, getLogsDirPath } from '@/util/paths';
import * as rfs from 'rotating-file-stream';
import path from 'path';
import fs from 'node:fs';
import { createLogger } from '@/util/base-log';

const streamErrorLogPath = getErrorLogsPath();

// 确保日志目录存在
const logDir = path.dirname(streamErrorLogPath);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

let logStream: rfs.RotatingFileStream | null = null;
let errorLogStream: rfs.RotatingFileStream | null = null;

const streamConfig = {
  interval: '1h',
  compress: 'gzip',
  size: '10M',
  path: getLogsDirPath(),
  teeToStdout: process.env.KWAIPILOT_DEVELOPMENT === 'true' || process.env.KWAIPILOT_AGENT_USE_SOCKET === 'true',
  maxFiles: 30,
  maxSize: '100M',
};

function writeStreamError(message: string, error: any) {
  const timestamp = new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }).replace(/,/g, '');
  const errorMessage = `[${timestamp}] ${message}: ${error instanceof Error ? error.stack : JSON.stringify(error)}\n`;
  try {
    fs.appendFileSync(streamErrorLogPath, errorMessage);
  } catch (e) {
    // 如果连写入错误日志都失败了，只能输出到控制台
    console.error('Failed to write to stream error log:', e);
  }
}

export function getLogStream() {
  if (!logStream) {
    logStream = rfs.createStream('core.log', streamConfig);

    logStream.on('error', (err) => {
      writeStreamError('Log stream error', err);
    });

    process.on('beforeExit', () => {
      if (logStream) {
        try {
          logStream.end();
        } catch (err) {
          writeStreamError('Error closing log stream', err);
        }
        logStream = null;
      }
    });
  }
  return logStream;
}

export function getErrorLogStream() {
  if (!errorLogStream) {
    errorLogStream = rfs.createStream('error.log', streamConfig);

    errorLogStream.on('error', (err) => {
      writeStreamError('Error log stream error', err);
    });

    process.on('beforeExit', () => {
      if (errorLogStream) {
        try {
          errorLogStream.end();
        } catch (err) {
          writeStreamError('Error closing error log stream', err);
        }
        errorLogStream = null;
      }
    });
  }
  return errorLogStream;
}

const CoreLogging = createLogger('core');
const consoleLog = new CoreLogging('common-console');

// patch console log
export function setupCoreLogging() {
  console.log = (message: any, ...optionalParams: any[]) => {
    consoleLog.info(message, ...optionalParams);
  };
  console.error = (message: any, ...optionalParams: any[]) => {
    consoleLog.error(message, ...optionalParams);
  };
  console.warn = (message: any, ...optionalParams: any[]) => {
    consoleLog.warn(message, ...optionalParams);
  };
  console.debug = (message: any, ...optionalParams: any[]) => {
    consoleLog.debug(message, ...optionalParams);
  };
  console.log('[info] Starting kwaipilot core...');
}
