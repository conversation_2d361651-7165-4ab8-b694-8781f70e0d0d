export const code_dense_dim = 896;
export const nl_dense_dim = 1024;
export const AGENT_VERSION = '1.0.0';
export const generateCollectionName = (repo_dir: string) => {
  // Check if the repo_dir contains Chinese characters
  const containsChinese = /[\u4e00-\u9fa5]/.test(repo_dir);

  // Process the collection name
  // Replace both forward slashes (/) and backslashes (\) for Windows compatibility
  let collection_name = repo_dir.replace(/[-\/\\]/g, '_');

  // If it contains Chinese characters, replace all Chinese characters with a single '_cn_'
  if (containsChinese) {
    collection_name = collection_name.replace(/[\u4e00-\u9fa5]+/g, '_cn_');
  }
  // Remove or replace special characters that LanceDB doesn't support
  // Replace spaces, dots, and other special characters with underscores
  collection_name = collection_name.replace(/[^\w_]/g, '_');

  // Ensure it doesn't start with a number (if LanceDB requires this)
  if (/^\d/.test(collection_name)) {
    collection_name = `c_${collection_name}`;
  }

  // Remove or replace special characters that LanceDB doesn't support
  // Replace spaces, dots, and other special characters with underscores
  collection_name = collection_name.replace(/[^\w_]/g, '_');

  // Ensure it doesn't start with a number (if LanceDB requires this)
  if (/^\d/.test(collection_name)) {
    collection_name = `c_${collection_name}`;
  }

  return collection_name;
};

export const getCacheCollectionName = (collection_name: string) => {
  return `${collection_name}_cache`;
};

export const getCodeCollectionName = (collection_name: string) => {
  return `${collection_name}_code`;
};
export const getNonCodeCollectionName = (collection_name: string) => {
  return `${collection_name}_non_code`;
};
export const AGENT_NAMESPACE = 'agent-state';
export const ASSISTANT_NAMESPACE = 'assistant-agent';

export const MAX_INDEX_FILE_COUNT = 10000;

export const MCP_TOOL_MAX_LIMIT = 40;