import { ContextNode } from '../node/ContextNode';
import { process_code_files, process_none_code_files } from './code_utils';
import * as fs from 'fs';
import type { Stats } from 'fs';
import * as path from 'path';
import { isSameLang, splitNodeWithLargeSnippet } from './program_utils';
import { AutoTokenizer } from '../chunk/Tokenlizer';
import readdirp from 'readdirp';
import { filename_to_lang } from '../chunk/code';
import { AGENT_NAMESPACE, generateCollectionName, getNonCodeCollectionName } from '@/util/const';
import { CodeEmbedding } from '@/embedding/CodeEmbedding';
import { NonCodeEmbedding } from '@/embedding/NoneCodeEmbedding';
import { CodeInfoTable } from '@/db/sqlite/tables';
import { CodeInfo } from '@/db/sqlite/tables/code-info';
import { NodeGraphTable, NodeGraphType, NodeGraph } from '@/db/sqlite/tables/node-graph';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import { LanceDB } from '@/db/lancedb';

export class ContextGraph {
  private readonly logger = new Logger('ContextGraph');
  repoDir: string = '';
  total_file_num: number = 0;
  fileList: string[] = [];
  private _nodeIdDict: Map<string, ContextNode> = new Map();
  private _nodeNameDict: Map<string, ContextNode[]> = new Map();
  private _splitNodeList: ContextNode[] = [];
  static cg: ContextGraph;
  collectionName: string = '';
  constructor(dir: string) {
    this.repoDir = dir;
    this.collectionName = generateCollectionName(dir);
  }

  async initContextNodes(files?: string[]) {
    if (files) {
      this.fileList = files;
      this.total_file_num = files.length;
    } else {
      this.fileList = await this.getFileList(this.repoDir, true);
      this.total_file_num = this.fileList.length;
    }
    const context_nodes = await process_code_files(this.fileList);
    for (const context_node of context_nodes) {
      context_node.setRelativePath(this.repoDir);
      this.addNodeToContextDict(context_node);
    }
  }
  async clearBeforeIndex(file: string, exsits: CodeInfo[]) {
    try {
      const codeInfotable = await CodeInfoTable.getCodeInfoTable(this.collectionName);
      const nodeGraphTable = await NodeGraphTable.getNodeGraphTable(this.collectionName);
      const table = await LanceDB.getTable(this.collectionName);
      const condition = `file_path = "${file}"`;
      // const result = await LanceDB.getTableData(table.query().where(condition));
      await table.delete(condition);
      await codeInfotable.deleteByFilePath(file);
      await nodeGraphTable.deleteByNodesId(exsits.map((item) => item.node_id));
    } catch (error) {
      this.logger.error(`clearBeforeIndex failed, 清除之前的数据失败`, error);
    }
  }
  async indexCodeFile(file: string) {
    try {
      let startTime = Date.now();
      const codeInfotable = await CodeInfoTable.getCodeInfoTable(this.collectionName);
      const relativePath = path.relative(this.repoDir, file);
      const exsits = await codeInfotable.findByFilePath(relativePath);
      if (exsits && exsits.length > 0) {
        // 清除掉之前的数据
        await this.clearBeforeIndex(relativePath, exsits);
      }
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'INDEXING_clearBeforeIndex',
        millis: Date.now() - startTime,
        extra4: file,
        extra6: 'success'
      });
      this.logger.reportUserAction({
        key: 'INDEXING',
        type: 'clearBeforeIndex',
        subType: 'success',
        content: JSON.stringify({
          repo_dir: this.repoDir,
          file: file,
          time: Date.now() - startTime
        })
      });
      startTime = Date.now();
      const context_nodes = await process_code_files([file]);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'INDEXING_process_code_files',
        millis: Date.now() - startTime,
        extra4: file,
        extra6: 'success'
      });
      this.logger.reportUserAction({
        key: 'INDEXING',
        type: 'process_code_files',
        subType: 'success',
        content: JSON.stringify({
          repo_dir: this.repoDir,
          file: file,
          time: Date.now() - startTime,
          context_nodes: context_nodes.length
        })
      });
      startTime = Date.now();
      this.logger.debug(`indexCodeFile----${file}`);
      if (context_nodes.length === 0) {
        await this.resetEnvs();
        return;
      }
      if (exsits && exsits.length > 0) {
        // 以之前的nodeid 来赋值本次新生成的context_node
        for (const context_node of context_nodes) {
          const codeInfo = exsits.find((item) => item.name === context_node.name);
          if (codeInfo) {
            context_node.setNodeId(codeInfo.node_id);
          }
        }
      }
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'INDEXING_addNodeToContextDict',
        millis: Date.now() - startTime,
        extra4: file,
        extra6: 'success'
      });
      this.logger.reportUserAction({
        key: 'INDEXING',
        type: 'addNodeToContextDict',
        subType: 'success',
        content: JSON.stringify({
          repo_dir: this.repoDir,
          file: file,
          time: Date.now() - startTime
        })
      });
      startTime = Date.now();
      for (const context_node of context_nodes) {
        context_node.setRelativePath(this.repoDir);
        this.addNodeToContextDict(context_node);
      }
      this.logger.debug(`设置关联关系完成`);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'INDEXING_setConnectionForNode',
        millis: Date.now() - startTime,
        extra4: file,
        extra6: 'success'
      });
      this.logger.reportUserAction({
        key: 'INDEXING',
        type: 'setConnectionForNode',
        subType: 'success',
        content: JSON.stringify({
          repo_dir: this.repoDir,
          file: file,
          time: Date.now() - startTime,
          context_nodes: context_nodes.length
        })
      });
      startTime = Date.now();
      // 这里需要先将所有的 snippet 信息插入到数据库，以便查询所有的依赖信息

      await codeInfotable.batchCreate(context_nodes.map((node) => node.toCodeInfo()));
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'INDEXING_batchCreate',
        millis: Date.now() - startTime,
        extra4: file,
        extra6: 'success'
      });
      this.logger.reportUserAction({
        key: 'INDEXING',
        type: 'batchCreate',
        subType: 'success',
        content: JSON.stringify({
          file: file,
          time: Date.now() - startTime,
          context_nodes: context_nodes.length
        })
      });
      startTime = Date.now();
      this.logger.debug(`将代码片段信息保存到数据库成功`);
      await this.buildContextGraph();
      this.logger.debug(`构建上下文图谱完成`);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'INDEXING_buildContextGraph',
        millis: Date.now() - startTime,
        extra4: file,
        extra6: 'success'
      });
      this.logger.reportUserAction({
        key: 'INDEXING',
        type: 'buildContextGraph',
        subType: 'success',
        content: JSON.stringify({
          repo_dir: this.repoDir,
          file: file,
          time: Date.now() - startTime,
          context_nodes: context_nodes.length
        })
      });
      startTime = Date.now();
      // Insert into database
      const codeData = this.getContextGraphNodeList();
      const collectionName = generateCollectionName(this.repoDir);
      const embedding = new CodeEmbedding(collectionName);
      await embedding.insertData(collectionName, codeData);
      this.logger.debug(`将代码片段信息保存到LanceDB成功${file}`);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'INDEXING_insertData',
        millis: Date.now() - startTime,
        extra4: file,
        extra6: 'success'
      });
      this.logger.reportUserAction({
        key: 'INDEXING',
        type: 'insertData',
        subType: 'success',
        content: JSON.stringify({
          repo_dir: this.repoDir,
          file: file,
          time: Date.now() - startTime,
          context_nodes: codeData.length
        })
      });
      await this.resetEnvs();
      this.logger.info(`index files done, 索引文件完成`);
    } catch (error: any) {
      this.logger.error(`index code file failed, 索引代码文件失败`, error);
      const errorStack = new Error().stack || 'No stack trace available';
      this.logger.error(`Error stack trace: ${errorStack}`);

      // Log detailed error information
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error.code,
        errno: error.errno,
        sql: error.sql,
        stack: error.stack,
        file: file
      };
      this.logger.error('Detailed error information:', errorDetails);

      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'indexCodeFileError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: errorStack
      });
      throw error;
    }
  }
  async indexNonCodeFile(file: string) {
    try {
      this.logger.debug(`indexNonCodeFile----${file}`);
      const context_nodes = await process_none_code_files([file]);
      for (const context_node of context_nodes) {
        context_node.setRelativePath(this.repoDir);
        this.addNodeToContextDict(context_node);
      }
      // 插入到数据库
      const collectionName = generateCollectionName(this.repoDir);
      const noneCodeCollectionName = getNonCodeCollectionName(collectionName);
      const embedding = new NonCodeEmbedding(noneCodeCollectionName);
      if (file.toLowerCase().indexOf('readme') > -1) {
        await embedding.insertData(collectionName, context_nodes);
      } else {
        await embedding.insertData(noneCodeCollectionName, context_nodes);
      }
      this.logger.debug(`将非代码片段信息保存到LanceDB成功${file}`);
      await this.resetEnvs();
      this.logger.info(`index non code files done, 索引非代码文件完成`);
    } catch (error: any) {
      this.logger.error(`index non code file failed, 索引非代码文件失败`, error);
      const errorStack = new Error().stack || 'No stack trace available';
      this.logger.error(`Error stack trace: ${errorStack}`);

      // Log detailed error information
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error.code,
        errno: error.errno,
        sql: error.sql,
        stack: error.stack,
        file: file
      };
      this.logger.error('Detailed error information:', errorDetails);

      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'indexNonCodeFileError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: errorStack
      });
      throw error;
    }
  }

  async resetEnvs() {
    this._nodeIdDict.clear();
    this._nodeNameDict.clear();
    this._splitNodeList = [];
    this.total_file_num = 0;
    this.fileList = [];
  }

  async getPossibleNodesByName(name: string): Promise<CodeInfo[]> {
    const codeInfotable = await CodeInfoTable.getCodeInfoTable(this.collectionName);
    const nodes = await codeInfotable.findByName(name);
    return nodes;
  }
  async getFileList(repoDir: string, is_code_files: boolean): Promise<string[]> {
    const fileList: string[] = [];
    // todo：这个地方增加根据 ignore 文件返回
    const entries = await readdirp(repoDir, {
      alwaysStat: true,
      fileFilter: (entry) => {
        if (entry.fullPath.indexOf('node_modules') !== -1) {
          return false;
        }
        if (entry.fullPath.indexOf('.git') !== -1) {
          return false;
        }
        const stat = entry.stats as Stats;
        if (stat.isFile() && stat.size > 500000) {
          this.logger.warn(`file ${entry.fullPath} is too large, skip it`);
          return false;
        }
        if (is_code_files) {
          // 代码文件
          if (!filename_to_lang(entry.fullPath)) {
            return false;
          }
        } else {
          // 非代码文件
          if (filename_to_lang(entry.fullPath)) {
            return false;
          }
        }
        return true;
      },
      directoryFilter: (entry) => {
        if (entry.fullPath.indexOf('node_modules') !== -1) {
          return false;
        }
        if (entry.fullPath.indexOf('.git') !== -1) {
          return false;
        }
        return true;
      }
    });
    for await (const entry of entries) {
      if (entry.stats.isFile()) {
        fileList.push(entry.fullPath);
      }
    }
    return fileList;
  }
  // 通过函数名，获取可能的父函数
  getPossibleParentNodesByName(name: string): ContextNode[] {
    return this._nodeNameDict.get(name) || [];
  }

  // TODO：这里想放到最后一步，防止中间出现脏数据
  async setConnectionForNode(node: ContextNode): Promise<Array<Omit<NodeGraph, 'id'>>> {
    const relationships: Array<Omit<NodeGraph, 'id'>> = [];

    // Collect dependency relationships
    for (const refCallName of node.callee_name_references) {
      for (const refCallNode of await this.getPossibleNodesByName(refCallName)) {
        if (!isSameLang(node.language, refCallNode.language)) {
          continue;
        }
        relationships.push({
          node_id: node.node_id,
          type: NodeGraphType.dependency,
          sub_node_id: refCallNode.node_id
        });
      }
    }

    // Collect parent dependency relationships
    const anonymous_parent_function_name = node.anonymous_parent_function_name;
    if (!anonymous_parent_function_name) {
      return relationships;
    }
    const possible_parents = this.getPossibleParentNodesByName(anonymous_parent_function_name);

    for (const possible_parent of possible_parents) {
      if (
        possible_parent.file_path === node.file_path &&
        possible_parent.line_from <= node.line_from &&
        possible_parent.line_to >= node.line_to
      ) {
        relationships.push({
          node_id: node.node_id,
          type: NodeGraphType.parentDependency,
          sub_node_id: possible_parent.node_id
        });
      }
    }

    return relationships;
  }

  *iterContextNodes(): IterableIterator<ContextNode> {
    for (const node of this._nodeIdDict.values()) {
      yield node;
    }
  }
  async buildContextGraph() {
    // Build node relationships
    // const nodeGraphTable = await NodeGraphTable.getNodeGraphTable(this.collectionName);

    // const relationships: Array<Omit<NodeGraph, 'id'>> = [];
    // for (const node of this.iterContextNodes()) {
    //   const relation = await this.setConnectionForNode(node);
    //   if (relation.length > 0) {
    //     relationships.push(...relation);
    //   }
    // }
    // if (relationships.length > 0) {
    //   await nodeGraphTable.batchCreate(relationships);
    // }

    // Split large nodes
    const tokenizer = new AutoTokenizer();

    for (const node of this.iterContextNodes()) {
      await this.splitLargeNode(node, tokenizer);
    }
  }

  async splitLargeNode(node: ContextNode, tokenizer: AutoTokenizer): Promise<void> {
    const splitNodes = splitNodeWithLargeSnippet(node, tokenizer);
    this._splitNodeList.push(...splitNodes);
  }

  async saveContextGraph(outFile?: string): Promise<string> {
    const result = [];
    for (const node of this._splitNodeList) {
      result.push(node.toDict());
    }

    if (!outFile) {
      const repoName = this.repoDir.split('/').pop() || '';
      outFile = `./tmp/${repoName}-tree-sitter.json`;
    }

    await fs.promises.mkdir(path.dirname(outFile), { recursive: true });
    await fs.promises.writeFile(outFile, JSON.stringify(result, null, 1), 'utf-8');

    return outFile;
  }

  addNodeToContextDict(contextNode: ContextNode): void {
    this._nodeIdDict.set(contextNode.id, contextNode);

    if (contextNode.code_type === 'Function') {
      // 根据函数名，添加到对应的函数名列表中，TODO：这里取出来的函数名有问题，需要处理
      if (this._nodeNameDict.has(contextNode.name)) {
        this._nodeNameDict.get(contextNode.name)?.push(contextNode);
      } else {
        this._nodeNameDict.set(contextNode.name, [contextNode]);
      }
    }
  }

  getContextGraphNodeList(): ContextNode[] {
    const result: ContextNode[] = [];
    for (const node of this._splitNodeList) {
      result.push(node);
    }
    return result;
  }
  static getContextGraph(dir: string) {
    if (!ContextGraph.cg) {
      ContextGraph.cg = new ContextGraph(dir);
    }

    return ContextGraph.cg;
  }
}
